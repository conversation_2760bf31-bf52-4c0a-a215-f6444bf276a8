const express = require("express");
const { check } = require("express-validator");
const {
  getAllContent,
  getContent,
  createContent,
  updateContent,
  deleteContent,
  getSellerContent,
  getSellerContentById,
  getContentCategories,
  getTrendingContent,
  toggleContentStatus,
  getContentAccess,
  getContentPreview,
  getPreviewStatus,
  testPreviewGeneration,
} = require("../controllers/content");

const { protect, authorize } = require("../middleware/auth");
const { upload } = require("../utils/fileUpload");
const { getFileUrl, getSignedUrl } = require("../utils/storageHelper");
const { convertContentS3Urls } = require("../middleware/s3UrlHandler");

const router = express.Router();

// Public routes with S3 URL conversion
router.get("/", convertContentS3Urls, getAllContent);
router.get("/categories", getContentCategories);
router.get("/trending", convertContentS3Urls, getTrendingContent);
router.get("/:id/preview", convertContentS3Urls, getContentPreview);
router.get("/:id/preview-status", getPreviewStatus);
router.get("/:id", convertContentS3Urls, getContent);

// Handle CORS preflight for streaming endpoint
router.options("/stream/:fileKey(*)", (req, res) => {
  const origin = req.headers.origin;
  const allowedOrigins = [
    "http://localhost:3000",
    "http://localhost:5173",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:5173",
    "https://localhost:3000",
    "https://localhost:5173",
    "https://xosportshub.thefabaf.com",
    "https://xosports.thefabaf.com",
    "http://xosportshub.thefabaf.com",
    "http://xosports.thefabaf.com",
    process.env.FRONTEND_URL,
  ].filter(Boolean);

  if (allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization');
  res.setHeader('Access-Control-Expose-Headers', 'Content-Range, Accept-Ranges, Content-Length');
  res.status(200).end();
});

// Stream video files from S3 with proper headers for video playback
router.get(
  "/stream/:fileKey(*)",
  async (req, res, next) => {
    try {
      const fileKey = req.params.fileKey;
      const range = req.headers.range;
      const token = req.query.token || req.headers.authorization?.replace('Bearer ', '');

      // Verify token manually since we're bypassing the protect middleware
      console.log(`[Video Stream] Token from query: ${req.query.token ? 'present' : 'missing'}`);
      console.log(`[Video Stream] Token from header: ${req.headers.authorization ? 'present' : 'missing'}`);
      console.log(`[Video Stream] Final token: ${token ? 'present' : 'missing'}`);

      if (!token) {
        console.log(`[Video Stream] No token provided`);
        return res.status(401).json({
          success: false,
          message: "Access token is required"
        });
      }

      // Verify the JWT token
      const jwt = require('jsonwebtoken');
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.user = decoded;
        console.log(`[Video Stream] Token verified successfully for user: ${decoded.id}`);
      } catch (error) {
        console.log(`[Video Stream] Token verification failed:`, error.message);
        return res.status(401).json({
          success: false,
          message: "Invalid token"
        });
      }

      if (!fileKey) {
        return res.status(400).json({
          success: false,
          message: "File key is required"
        });
      }

      console.log(`[Video Stream] Streaming video for: ${fileKey}`);

      const { getS3Instance, isUsingS3Storage } = require("../utils/storageHelper");

      if (!isUsingS3Storage()) {
        return res.status(400).json({
          success: false,
          message: "S3 storage not configured"
        });
      }

      const s3 = getS3Instance();
      if (!s3) {
        return res.status(500).json({
          success: false,
          message: "S3 instance not available"
        });
      }

      // Get object metadata first
      const headParams = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: fileKey
      };

      const headResult = await s3.headObject(headParams).promise();
      const fileSize = headResult.ContentLength;
      const contentType = headResult.ContentType || 'video/mp4';

      // Set CORS headers
      const origin = req.headers.origin;
      const allowedOrigins = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "https://localhost:3000",
        "https://localhost:5173",
        "https://xosportshub.thefabaf.com",
        "https://xosports.thefabaf.com",
        "http://xosportshub.thefabaf.com",
        "http://xosports.thefabaf.com",
        process.env.FRONTEND_URL,
      ].filter(Boolean);

      if (allowedOrigins.includes(origin)) {
        res.setHeader('Access-Control-Allow-Origin', origin);
      }
      res.setHeader('Access-Control-Allow-Credentials', 'true');
      res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization');
      res.setHeader('Access-Control-Expose-Headers', 'Content-Range, Accept-Ranges, Content-Length');

      // Handle range requests for video seeking
      if (range) {
        const parts = range.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
        const chunksize = (end - start) + 1;

        const getParams = {
          Bucket: process.env.AWS_BUCKET_NAME,
          Key: fileKey,
          Range: `bytes=${start}-${end}`
        };

        res.status(206);
        res.setHeader('Content-Range', `bytes ${start}-${end}/${fileSize}`);
        res.setHeader('Accept-Ranges', 'bytes');
        res.setHeader('Content-Length', chunksize);
        res.setHeader('Content-Type', contentType);

        const stream = s3.getObject(getParams).createReadStream();
        stream.pipe(res);
      } else {
        // Full file request
        const getParams = {
          Bucket: process.env.AWS_BUCKET_NAME,
          Key: fileKey
        };

        res.setHeader('Content-Length', fileSize);
        res.setHeader('Content-Type', contentType);
        res.setHeader('Accept-Ranges', 'bytes');

        const stream = s3.getObject(getParams).createReadStream();
        stream.pipe(res);
      }

    } catch (error) {
      console.error('[Video Stream] Error streaming video:', error);
      if (error.code === 'NoSuchKey') {
        return res.status(404).json({
          success: false,
          message: "Video file not found"
        });
      }
      next(error);
    }
  }
);

// Protected routes
router.use(protect);



// Secure content access routes
router.get("/:id/access", convertContentS3Urls, getContentAccess);

// Seller routes
router.get("/seller/me", authorize("seller"), convertContentS3Urls, getSellerContent);
router.get("/seller/:id", authorize("seller"), convertContentS3Urls, getSellerContentById);

router.post(
  "/",
  authorize("seller"),
  [
    // Required fields that are visible in frontend
    check("title", "Title is required").not().isEmpty()
      .isLength({ max: 100 }).withMessage("Title cannot exceed 100 characters"),
    check("description", "Description is required").not().isEmpty(),
    check("sport", "Sport is required").not().isEmpty(),
    check("contentType", "Content type is required").not().isEmpty(),
    check("fileUrl", "File URL is required").not().isEmpty(),
    check("category", "Category is required").not().isEmpty(),
    check("difficulty", "Difficulty level is required").not().isEmpty(),
    check("aboutCoach", "About coach information is required").not().isEmpty(),
    check("strategicContent", "Strategic content description is required")
      .not()
      .isEmpty(),
    check("saleType", "Sale type is required").not().isEmpty(),

    // Optional fields that are used in frontend with default values
    check("language", "Language is required")
      .optional()
      .isIn([
        "English",
        "Spanish",
        "French",
        "German",
        "Italian",
        "Portuguese",
        "Chinese",
        "Japanese",
        "Korean",
        "Other",
      ]),

    // COMMENTED OUT - Fields not used in current frontend UI
    // check('videoLength', 'Video length must be a positive number').optional().isFloat({ min: 0 }),
    // check('prerequisites', 'Prerequisites must be an array').optional().isArray(),
    // check('learningObjectives', 'Learning objectives must be an array').optional().isArray(),
    // check('equipment', 'Equipment must be an array').optional().isArray()
  ],
  createContent
);

router
  .route("/:id")
  .put(authorize("seller", "admin"), updateContent)
  .delete(authorize("seller", "admin"), deleteContent);

// Toggle content status route
router.put(
  "/:id/toggle-status",
  authorize("seller", "admin"),
  toggleContentStatus
);

// File upload route with extended timeout for large files
router.post(
  "/upload",
  protect,
  authorize("seller"),
  // Add timeout middleware for large uploads
  (req, res, next) => {
    // Set longer timeout for this route (1 hour for large video uploads)
    req.setTimeout(3600000); // 1 hour (3600 seconds)
    res.setTimeout(3600000); // 1 hour (3600 seconds)

    // Check content length header
    const contentLength = parseInt(req.headers['content-length']);
    if (contentLength) {
      // For multipart uploads, we can't determine the file type from content-type header
      // (it will be multipart/form-data), so we'll use the maximum allowed size (1GB)
      // The actual file type validation will be done by multer's fileFilter
      const maxSize = 1024 * 1024 * 1024; // 1GB - maximum allowed for any file type

      if (contentLength > maxSize) {
        const maxSizeMB = maxSize / (1024 * 1024);
        return res.status(413).json({
          success: false,
          message: `File size exceeds the maximum limit of ${maxSizeMB}MB`
        });
      }
    }

    console.log(`[Upload] Starting upload for user: ${req.user.id}`);
    console.log(`[Upload] Content-Length: ${req.headers['content-length']} bytes`);

    next();
  },
  upload.single("file"),
  async (req, res, next) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: "No file uploaded"
        });
      }

      const fileUrl = getFileUrl(req.file);
      const fileSizeMB = Math.round(req.file.size / (1024 * 1024));

      console.log(`[Upload] Upload completed successfully: ${req.file.originalname} (${fileSizeMB}MB)`);

      res.status(200).json({
        success: true,
        data: {
          fileUrl: fileUrl,
          fileName: req.file.originalname,
          fileType: req.file.mimetype,
          fileSize: req.file.size,
          fileSizeMB: fileSizeMB
        },
        message: `File uploaded successfully (${fileSizeMB}MB). Preview will be generated when content is created.`
      });
    } catch (error) {
      console.error('[Upload] Upload error:', error);
      next(error);
    }
  }
);

// Chunked upload initialization
router.post(
  "/upload/init",
  protect,
  authorize("seller"),
  async (req, res, next) => {
    try {
      const { fileName, fileSize, fileType, totalChunks } = req.body;

      if (!fileName || !fileSize || !fileType || !totalChunks) {
        return res.status(400).json({
          success: false,
          message: "Missing required fields: fileName, fileSize, fileType, totalChunks"
        });
      }

      // Validate file type and size before starting upload
      const { validateFileForChunkedUpload } = require('../utils/fileUpload');
      const validation = validateFileForChunkedUpload(fileName, fileSize, fileType);

      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          message: validation.message
        });
      }

      // Generate unique upload session ID
      const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Store upload session info (in production, use Redis or database)
      const uploadSession = {
        uploadId,
        fileName,
        fileSize,
        fileType,
        totalChunks,
        uploadedChunks: [],
        createdAt: new Date(),
        lastActivity: new Date(),
        userId: req.user.id
      };

      // Store in memory for now (in production, use Redis)
      global.uploadSessions = global.uploadSessions || new Map();
      global.uploadSessions.set(uploadId, uploadSession);

      // Add session cleanup protection (remove sessions older than 2 hours)
      const cleanupOldSessions = async () => {
        const now = new Date();
        const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);

        for (const [sessionId, session] of global.uploadSessions.entries()) {
          if (session.lastActivity < twoHoursAgo) {
            console.log(`[ChunkedUpload] Cleaning up expired session: ${sessionId} for file: ${session.fileName}`);

            // Clean up uploaded chunks for expired session
            const uploadedChunks = session.uploadedChunks.filter(chunk => chunk);
            if (uploadedChunks.length > 0) {
              console.log(`[ChunkedUpload] Cleaning up ${uploadedChunks.length} chunks from expired session: ${sessionId}`);

              try {
                const { cleanupTempChunks } = require('../utils/chunkAssembler');
                await cleanupTempChunks(uploadedChunks);
                console.log(`[ChunkedUpload] Successfully cleaned up chunks from expired session: ${sessionId}`);
              } catch (cleanupError) {
                console.error(`[ChunkedUpload] Error cleaning up chunks from expired session ${sessionId}:`, cleanupError);
              }
            }

            global.uploadSessions.delete(sessionId);
          }
        }
      };

      // Run cleanup occasionally (async, don't wait for completion)
      if (Math.random() < 0.1) { // 10% chance
        cleanupOldSessions().catch(error => {
          console.error('[ChunkedUpload] Error during session cleanup:', error);
        });
      }

      console.log(`[ChunkedUpload] Initialized upload session: ${uploadId} for file: ${fileName} (${Math.round(fileSize / (1024 * 1024))}MB)`);

      res.status(200).json({
        success: true,
        data: {
          uploadId,
          chunkSize: 5 * 1024 * 1024, // 5MB chunks
          maxRetries: 3
        },
        message: "Upload session initialized"
      });
    } catch (error) {
      console.error('[ChunkedUpload] Init error:', error);
      next(error);
    }
  }
);

// Chunked upload - upload individual chunk
router.post(
  "/upload/chunk",
  protect,
  authorize("seller"),
  // Use multer upload middleware first to parse FormData
  upload.single("chunk"),
  async (req, res, next) => {
    try {
      const { uploadId, chunkIndex } = req.body;

      console.log(`[ChunkedUpload] Received chunk upload request:`, {
        uploadId,
        chunkIndex,
        bodyKeys: Object.keys(req.body),
        hasFile: !!req.file,
        fileSize: req.file?.size
      });

      if (!uploadId) {
        console.error('[ChunkedUpload] Missing uploadId in request body:', req.body);
        return res.status(400).json({
          success: false,
          message: "Missing required field: uploadId"
        });
      }

      if (chunkIndex === undefined || !req.file) {
        return res.status(400).json({
          success: false,
          message: "Missing required fields: chunkIndex or chunk file"
        });
      }

      // Get upload session
      global.uploadSessions = global.uploadSessions || new Map();
      const uploadSession = global.uploadSessions.get(uploadId);

      console.log(`[ChunkedUpload] Looking for session ${uploadId}, found:`, !!uploadSession);
      console.log(`[ChunkedUpload] Total active sessions:`, global.uploadSessions.size);

      if (!uploadSession) {
        console.error(`[ChunkedUpload] Upload session not found: ${uploadId}`);
        console.error('[ChunkedUpload] Available sessions:', Array.from(global.uploadSessions.keys()));
        return res.status(404).json({
          success: false,
          message: "Upload session not found or expired"
        });
      }

      // Verify user owns this upload session
      if (uploadSession.userId !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: "Unauthorized access to upload session"
        });
      }

      const chunkIndexNum = parseInt(chunkIndex);

      // Update session activity
      uploadSession.lastActivity = new Date();

      // Store chunk info
      const tempPath = req.file.path || req.file.location;
      uploadSession.uploadedChunks[chunkIndexNum] = {
        chunkIndex: chunkIndexNum,
        size: req.file.size,
        uploadedAt: new Date(),
        tempPath: tempPath
      };

      console.log(`[ChunkedUpload] Stored chunk ${chunkIndexNum} with tempPath: ${tempPath}`);

      // **OPTIMIZATION: Generate preview from first chunk for video files**
      const isVideoFile = uploadSession.fileType.startsWith('video/');
      if (isVideoFile && chunkIndexNum === 0 && !uploadSession.previewGenerationStarted) {
        uploadSession.previewGenerationStarted = true;
        console.log(`[ChunkedUpload] Starting preview generation from first chunk: ${uploadSession.fileName}`);

        // Generate preview asynchronously from first chunk (non-blocking)
        setImmediate(async () => {
          try {
            const { generatePreviewFromChunk } = require('../utils/previewGenerator');
            const previewUrl = await generatePreviewFromChunk(
              tempPath,
              uploadSession.fileName,
              uploadSession.fileType
            );

            // Store preview URL in session for later use
            uploadSession.previewUrl = previewUrl;
            console.log(`[ChunkedUpload] Preview generated from first chunk: ${previewUrl}`);
          } catch (previewError) {
            console.error(`[ChunkedUpload] First chunk preview generation failed:`, previewError);
            uploadSession.previewGenerationFailed = true;
          }
        });
      }

      console.log(`[ChunkedUpload] Chunk ${chunkIndexNum + 1}/${uploadSession.totalChunks} uploaded for session: ${uploadId}`);

      res.status(200).json({
        success: true,
        data: {
          chunkIndex: chunkIndexNum,
          uploadedChunks: uploadSession.uploadedChunks.filter(chunk => chunk).length,
          totalChunks: uploadSession.totalChunks
        },
        message: `Chunk ${chunkIndexNum + 1} uploaded successfully`
      });
    } catch (error) {
      console.error('[ChunkedUpload] Chunk upload error:', error);
      next(error);
    }
  }
);

// Chunked upload completion - assemble chunks into final file
router.post(
  "/upload/complete",
  protect,
  authorize("seller"),
  async (req, res, next) => {
    try {
      const { uploadId } = req.body;

      if (!uploadId) {
        return res.status(400).json({
          success: false,
          message: "Missing uploadId"
        });
      }

      // Get upload session
      global.uploadSessions = global.uploadSessions || new Map();
      const uploadSession = global.uploadSessions.get(uploadId);

      if (!uploadSession) {
        return res.status(404).json({
          success: false,
          message: "Upload session not found or expired"
        });
      }

      // Verify user owns this upload session
      if (uploadSession.userId !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: "Unauthorized access to upload session"
        });
      }

      // Check if all chunks are uploaded
      const uploadedChunkCount = uploadSession.uploadedChunks.filter(chunk => chunk).length;
      if (uploadedChunkCount !== uploadSession.totalChunks) {
        return res.status(400).json({
          success: false,
          message: `Missing chunks. Expected ${uploadSession.totalChunks}, got ${uploadedChunkCount}`
        });
      }

      console.log(`[ChunkedUpload] Assembling ${uploadSession.totalChunks} chunks for session: ${uploadId}`);

      // Assemble chunks into final file
      const { assembleChunks } = require('../utils/chunkAssembler');
      const finalFileResult = await assembleChunks(uploadSession);

      // Clean up upload session
      global.uploadSessions.delete(uploadId);

      const fileSizeMB = Math.round(uploadSession.fileSize / (1024 * 1024));
      console.log(`[ChunkedUpload] Assembly completed for: ${uploadSession.fileName} (${fileSizeMB}MB)`);

      // Use preview generated from first chunk (if available)
      let previewUrl = null;
      const isVideoFile = uploadSession.fileType.startsWith('video/');

      if (isVideoFile) {
        if (uploadSession.previewUrl) {
          // Preview was successfully generated from first chunk
          previewUrl = uploadSession.previewUrl;
          console.log(`[ChunkedUpload] Using preview generated from first chunk: ${previewUrl}`);
        } else if (uploadSession.previewGenerationFailed) {
          // First chunk preview failed, fallback to traditional method
          console.log(`[ChunkedUpload] First chunk preview failed, using fallback method for: ${uploadSession.fileName}`);
          previewUrl = 'generating';

          // Schedule fallback preview generation asynchronously
          setImmediate(async () => {
            try {
              const { generatePreview } = require('../utils/previewGenerator');
              const isS3Upload = finalFileResult.fileUrl.includes('amazonaws.com') || finalFileResult.fileUrl.includes('s3.');
              const fallbackPreviewUrl = await generatePreview(
                'video',
                finalFileResult.fileUrl,
                uploadSession.fileName,
                isS3Upload
              );
              console.log(`[ChunkedUpload] Fallback preview generated: ${fallbackPreviewUrl}`);
            } catch (previewError) {
              console.error(`[ChunkedUpload] Fallback preview generation failed:`, previewError);
            }
          });
        } else {
          // Preview generation is still in progress or wasn't started
          console.log(`[ChunkedUpload] Preview generation in progress for: ${uploadSession.fileName}`);
          previewUrl = 'generating';
        }
      }

      const responseData = {
        fileUrl: finalFileResult.fileUrl,
        fileName: uploadSession.fileName,
        fileType: uploadSession.fileType,
        fileSize: uploadSession.fileSize,
        fileSizeMB: fileSizeMB
      };

      if (previewUrl) {
        responseData.previewUrl = previewUrl;
      }

      if (finalFileResult.cleanupResults) {
        responseData.cleanupResults = finalFileResult.cleanupResults;
      }

      const message = isVideoFile && previewUrl
        ? `File uploaded successfully (${fileSizeMB}MB) with preview generated.`
        : `File uploaded successfully (${fileSizeMB}MB).`;

      res.status(200).json({
        success: true,
        data: responseData,
        message: message
      });
    } catch (error) {
      console.error('[ChunkedUpload] Complete error:', error);
      next(error);
    }
  }
);

// Get upload session status
router.get(
  "/upload/status/:uploadId",
  protect,
  authorize("seller"),
  async (req, res, next) => {
    try {
      const { uploadId } = req.params;

      // Get upload session
      global.uploadSessions = global.uploadSessions || new Map();
      const uploadSession = global.uploadSessions.get(uploadId);

      if (!uploadSession) {
        return res.status(404).json({
          success: false,
          message: "Upload session not found or expired"
        });
      }

      // Verify user owns this upload session
      if (uploadSession.userId !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: "Unauthorized access to upload session"
        });
      }

      const uploadedChunkCount = uploadSession.uploadedChunks.filter(chunk => chunk).length;
      const progress = Math.round((uploadedChunkCount / uploadSession.totalChunks) * 100);

      res.status(200).json({
        success: true,
        data: {
          uploadId,
          progress,
          uploadedChunks: uploadedChunkCount,
          totalChunks: uploadSession.totalChunks,
          fileName: uploadSession.fileName,
          fileSize: uploadSession.fileSize,
          isComplete: uploadedChunkCount === uploadSession.totalChunks
        }
      });
    } catch (error) {
      console.error('[ChunkedUpload] Status error:', error);
      next(error);
    }
  }
);

// Cancel chunked upload and cleanup chunks
router.delete(
  "/upload/cancel/:uploadId",
  protect,
  authorize("seller"),
  async (req, res, next) => {
    try {
      const { uploadId } = req.params;

      // Get upload session
      global.uploadSessions = global.uploadSessions || new Map();
      const uploadSession = global.uploadSessions.get(uploadId);

      if (!uploadSession) {
        return res.status(404).json({
          success: false,
          message: "Upload session not found or expired"
        });
      }

      // Verify user owns this upload session
      if (uploadSession.userId !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: "Unauthorized access to upload session"
        });
      }

      console.log(`[ChunkedUpload] Cancelling upload session: ${uploadId} for file: ${uploadSession.fileName}`);

      // Clean up uploaded chunks
      const uploadedChunks = uploadSession.uploadedChunks.filter(chunk => chunk);
      if (uploadedChunks.length > 0) {
        console.log(`[ChunkedUpload] Cleaning up ${uploadedChunks.length} uploaded chunks`);

        const { cleanupTempChunks } = require('../utils/chunkAssembler');
        try {
          await cleanupTempChunks(uploadedChunks);
          console.log(`[ChunkedUpload] Successfully cleaned up chunks for cancelled upload: ${uploadId}`);
        } catch (cleanupError) {
          console.error(`[ChunkedUpload] Error cleaning up chunks for cancelled upload ${uploadId}:`, cleanupError);
          // Continue with session cleanup even if chunk cleanup fails
        }
      }

      // Remove upload session
      global.uploadSessions.delete(uploadId);

      const fileSizeMB = Math.round(uploadSession.fileSize / (1024 * 1024));
      console.log(`[ChunkedUpload] Upload cancelled and cleaned up: ${uploadSession.fileName} (${fileSizeMB}MB)`);

      res.status(200).json({
        success: true,
        message: `Upload cancelled and ${uploadedChunks.length} chunks cleaned up`,
        data: {
          uploadId,
          fileName: uploadSession.fileName,
          cleanedChunks: uploadedChunks.length
        }
      });
    } catch (error) {
      console.error('[ChunkedUpload] Cancel error:', error);
      next(error);
    }
  }
);

// Get file URL (handles both S3 and local files)
router.get(
  "/file/:fileKey(*)",
  protect,
  async (req, res, next) => {
    try {
      const fileKey = req.params.fileKey;

      if (!fileKey) {
        return res.status(400).json({
          success: false,
          message: "File key is required"
        });
      }

      console.log(`[File Access] Processing file access request for: ${fileKey}`);

      // Check if this is a local file path (starts with uploads/ or is a relative path)
      const isLocalFile = fileKey.startsWith('uploads/') ||
                         (!fileKey.includes('amazonaws.com') &&
                          !fileKey.includes('s3.') &&
                          !fileKey.startsWith('https://'));

      if (isLocalFile) {
        // For local files, return the direct URL path
        const localUrl = fileKey.startsWith('/uploads/') ? fileKey : `/uploads/${fileKey}`;
        console.log(`[File Access] Returning local file URL: ${localUrl}`);

        res.status(200).json({
          success: true,
          data: {
            fileUrl: localUrl,
            fileKey: fileKey,
            isLocal: true,
            accessType: 'direct'
          }
        });
      } else {
        // For S3 files, generate signed URL
        console.log(`[File Access] Generating signed URL for S3 file: ${fileKey}`);

        // Generate signed URL directly
        const signedUrl = getSignedUrl(fileKey);

        res.status(200).json({
          success: true,
          data: {
            signedUrl: signedUrl,
            fileKey: fileKey,
            isLocal: false,
            accessType: 'signed'
          }
        });
      }
    } catch (error) {
      console.error('[File Access] Error processing file access:', error);
      next(error);
    }
  }
);









// Test preview generation route (development only)
if (process.env.NODE_ENV !== 'production') {
  router.post(
    "/test-preview",
    protect,
    authorize("seller", "admin"),
    testPreviewGeneration
  );
}

module.exports = router;
